--- coma large ---
#set page(width: 450pt, margin: 1cm)

*Technische Universität Berlin* #h(1fr) *WiSe 2019/2020* \
*Fakultät II, Institut for Mathematik* #h(1fr) Woche 3 \
Sekretariat MA \
<PERSON><PERSON> <PERSON> \
<PERSON><PERSON>, <PERSON>

#v(3mm)
#align(center)[
  #set par(leading: 3mm)
  #text(1.2em)[*3. Übungsblatt Computerorientierte Mathematik II*] \
  *Abgabe: 03.05.2019* (bis 10:10 Uhr in MA 001) \
  *Alle Antworten sind zu beweisen.*
]

*1. Aufgabe* #h(1fr) (1 + 1 + 2 Punkte)

Ein _Binärbaum_ ist ein Wu<PERSON>baum, in dem jeder Knoten ≤ 2 Kinder hat.
Die Tiefe eines Knotens _v_ ist die Länge des eindeutigen Weges von der Wurzel
zu _v_, und die Höhe von _v_ ist die Länge eines längsten (absteigenden) Weges
von _v_ zu einem Blatt. Die Höhe des Baumes ist die Höhe der Wurzel.

#align(center, image("/assets/images/graph.png", width: 75%))
