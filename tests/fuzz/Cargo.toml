[package]
name = "typst-fuzz"
version.workspace = true
edition.workspace = true
publish = false

[package.metadata]
cargo-fuzz = true

[dependencies]
typst = { workspace = true }
typst-assets = { workspace = true, features = ["fonts"] }
typst-render = { workspace = true }
typst-syntax = { workspace = true }
comemo = { workspace = true }
libfuzzer-sys = { workspace = true }

[[bin]]
name = "parse"
path = "src/parse.rs"
test = false
doc = false

[[bin]]
name = "compile"
path = "src/compile.rs"
test = false
doc = false
