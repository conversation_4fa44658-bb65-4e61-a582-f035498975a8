[package]
name = "typst-tests"
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
publish = false

[[test]]
name = "tests"
path = "src/tests.rs"
harness = false

[features]
# Allow just compiling the parser when only testing typst-syntax. To do so,
# pass '--no-default-features' to 'cargo test'.
default = [
    # "typst-syntax" intentionally not present
    "typst",
    "typst-assets",
    "typst-dev-assets",
    "typst-html",
    "typst-library",
    "typst-pdf",
    "typst-render",
    "typst-svg",
    "typst-svg",
]

[dependencies]
typst-syntax = { workspace = true }
# Mark other Typst crates as optional so we can use '--no-default-features'
# to decrease compile times for parser testing.
typst = { workspace = true, optional = true }
typst-assets = { workspace = true, features = ["fonts"], optional = true }
typst-dev-assets = { workspace = true, optional = true }
typst-html = { workspace = true, optional = true }
typst-library = { workspace = true, optional = true }
typst-pdf = { workspace = true, optional = true }
typst-render = { workspace = true, optional = true }
typst-svg = { workspace = true, optional = true }
clap = { workspace = true }
comemo = { workspace = true }
ecow = { workspace = true }
oxipng = { workspace = true }
parking_lot = { workspace = true }
rayon = { workspace = true }
regex = { workspace = true }
rustc-hash = { workspace = true }
tiny-skia = { workspace = true }
unscanny = { workspace = true }
walkdir = { workspace = true }

[lints]
workspace = true
