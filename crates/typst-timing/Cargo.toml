[package]
name = "typst-timing"
description = "Performance timing for Typst."
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
categories = { workspace = true }
keywords = { workspace = true }
readme = { workspace = true }

[dependencies]
parking_lot = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }

[target.'cfg(target_arch = "wasm32")'.dependencies]
web-sys = { workspace = true, features = ["Window", "WorkerGlobalScope", "Performance"], optional = true }

[features]
wasm = ["dep:web-sys"]

[lints]
workspace = true
