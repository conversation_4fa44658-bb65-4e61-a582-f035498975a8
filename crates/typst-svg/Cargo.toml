[package]
name = "typst-svg"
description = "SVG exporter for Typst."
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
categories = { workspace = true }
keywords = { workspace = true }
readme = { workspace = true }

[dependencies]
typst-assets = { workspace = true }
typst-library = { workspace = true }
typst-macros = { workspace = true }
typst-timing = { workspace = true }
typst-utils = { workspace = true }
base64 = { workspace = true }
comemo = { workspace = true }
ecow = { workspace = true }
flate2 = { workspace = true }
hayro = { workspace = true }
image = { workspace = true }
rustc-hash = { workspace = true }
ttf-parser = { workspace = true }
xmlparser = { workspace = true }
xmlwriter = { workspace = true }

[lints]
workspace = true
