[package]
name = "typst"
description = "A new markup-based typesetting system that is powerful and easy to learn."
categories = ["compilers", "science"]
keywords = ["markup", "typesetting", "typst"]
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
readme = { workspace = true }

[dependencies]
typst-eval = { workspace = true }
typst-html = { workspace = true }
typst-layout = { workspace = true }
typst-library = { workspace = true }
typst-macros = { workspace = true }
typst-realize = { workspace = true }
typst-syntax = { workspace = true }
typst-timing = { workspace = true }
typst-utils = { workspace = true }
comemo = { workspace = true }
ecow = { workspace = true }
rustc-hash = { workspace = true }

[lints]
workspace = true
