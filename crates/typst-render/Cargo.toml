[package]
name = "typst-render"
description = "Raster image exporter for Typst."
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
categories = { workspace = true }
keywords = { workspace = true }
readme = { workspace = true }

[dependencies]
typst-assets = { workspace = true }
typst-library = { workspace = true }
typst-macros = { workspace = true }
typst-timing = { workspace = true }
bytemuck = { workspace = true }
comemo = { workspace = true }
hayro = { workspace = true }
image = { workspace = true }
pixglyph = { workspace = true }
resvg = { workspace = true }
tiny-skia = { workspace = true }
ttf-parser = { workspace = true }

[lints]
workspace = true
