[package]
name = "typst-ide"
description = "IDE functionality for Typst."
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
categories = { workspace = true }
keywords = { workspace = true }
readme = { workspace = true }

[dependencies]
typst = { workspace = true }
typst-eval = { workspace = true }
comemo = { workspace = true }
ecow = { workspace = true }
pathdiff = { workspace = true }
rustc-hash = { workspace = true }
serde = { workspace = true }
unscanny = { workspace = true }

[dev-dependencies]
typst-assets = { workspace = true, features = ["fonts"] }
typst-dev-assets = { workspace = true }
once_cell = { workspace = true }

[lints]
workspace = true
