[package]
name = "typst-syntax"
description = "Parser and syntax tree for Typst."
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
categories = { workspace = true }
keywords = { workspace = true }
readme = { workspace = true }

[dependencies]
typst-timing = { workspace = true }
typst-utils = { workspace = true }
ecow = { workspace = true }
rustc-hash = { workspace = true }
serde = { workspace = true }
toml = { workspace = true }
unicode-ident = { workspace = true }
unicode-math-class = { workspace = true }
unicode-script = { workspace = true }
unicode-segmentation = { workspace = true }
unscanny = { workspace = true }

[lints]
workspace = true
