# Language support
This VS Code extension provides minimal language support for Typst. It contains
a syntax definition and a language configuration for comment toggling,
autoclosing etc.

The extension was created for development purposes only. It is not maintained
and its grammar is buggy. For a more actively developed extension see the
third-party [Tinymist extension](https://github.com/Myriad-Dreamin/tinymist).

## Installation
The simplest way to install this extension (and keep it up-to-date) is to add a
symlink from `~/.vscode/extensions/typst-support` to
`path/to/typst/tools/support`.
