{"name": "typst-test-helper", "publisher": "typst", "displayName": "<PERSON><PERSON><PERSON> Test Helper", "description": "Helps to run, compare and update Typst tests.", "version": "0.0.1", "categories": ["Other"], "activationEvents": ["workspaceContains:tests/suite/playground.typ"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "typst-test-helper.refreshFromPreview", "title": "Refresh preview", "category": "<PERSON><PERSON><PERSON> Test Helper", "icon": "$(refresh)"}, {"command": "typst-test-helper.runFromPreview", "title": "Run test", "category": "<PERSON><PERSON><PERSON> Test Helper", "icon": "$(debug-start)", "enablement": "typst-test-helper.run<PERSON><PERSON><PERSON><PERSON>nabled"}, {"command": "typst-test-helper.saveFromPreview", "title": "Run and save reference output", "category": "<PERSON><PERSON><PERSON> Test Helper", "icon": "$(save)", "enablement": "typst-test-helper.run<PERSON><PERSON><PERSON><PERSON>nabled"}, {"command": "typst-test-helper.copyImageFilePathFromPreviewContext", "title": "Copy image file path", "category": "<PERSON><PERSON><PERSON> Test Helper"}, {"command": "typst-test-helper.increaseResolution", "title": "Render at higher resolution", "category": "<PERSON><PERSON><PERSON> Test Helper", "icon": "$(zoom-in)", "enablement": "typst-test-helper.run<PERSON><PERSON><PERSON><PERSON>nabled"}, {"command": "typst-test-helper.decreaseResolution", "title": "Render at lower resolution", "category": "<PERSON><PERSON><PERSON> Test Helper", "icon": "$(zoom-out)", "enablement": "typst-test-helper.run<PERSON><PERSON><PERSON><PERSON>nabled"}], "menus": {"editor/title": [{"when": "activeWebviewPanelId == typst-test-helper.preview", "command": "typst-test-helper.refreshFromPreview", "group": "navigation@1"}, {"when": "activeWebviewPanelId == typst-test-helper.preview", "command": "typst-test-helper.runFromPreview", "group": "navigation@2"}, {"when": "activeWebviewPanelId == typst-test-helper.preview", "command": "typst-test-helper.saveFromPreview", "group": "navigation@3"}, {"when": "activeWebviewPanelId == typst-test-helper.preview", "command": "typst-test-helper.increaseResolution", "group": "navigation@4"}, {"when": "activeWebviewPanelId == typst-test-helper.preview", "command": "typst-test-helper.decreaseResolution", "group": "navigation@4"}], "webview/context": [{"command": "typst-test-helper.copyImageFilePathFromPreviewContext", "when": "webviewId == typst-test-helper.preview && (webviewSection == png || webviewSection == ref)"}]}}, "scripts": {"build": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/node": "^24.0.4", "@types/vscode": "^1.101.0", "typescript": "^5.8.3"}, "dependencies": {"shiki": "^3.7.0"}, "engines": {"vscode": "^1.88.0"}, "__metadata": {"size": 35098973}}