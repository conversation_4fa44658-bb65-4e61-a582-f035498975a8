---
description: Typst 的教程。
---

# 制作模板
在本教程的前三章中，您已经学会了如何在 Typst 中编写文档、应用基本样式，以及深度自定义其外观以符合出版商的样式指南。由于您在上一章中编写的论文取得了巨大成功，您被要求为同一会议撰写后续文章。这次，您想要将在上一章中创建的样式转换为可重用的模板。在本章中，您将学习如何创建一个您和您的团队只需一个显示规则就可以使用的模板。让我们开始吧！

## 玩具模板 { #toy-template }
在 Typst 中，模板是您可以包装整个文档的函数。要学习如何做到这一点，让我们首先回顾如何编写您自己的函数。它们可以做您想要它们做的任何事情，所以为什么不疯狂一点呢？

```example
#let amazed(term) = box[✨ #term ✨]

您是 #amazed[美丽的]！
```

此函数接受单个参数 `term`，并返回一个内容块，其中 `term` 被星光包围。我们还将整个内容放在一个框中，这样我们惊叹的术语就不会被换行符与其星光分离。

Typst 附带的许多函数都有可选的命名参数。我们的
