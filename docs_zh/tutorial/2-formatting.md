---
description: Typst 的教程。
---

# 格式化
到目前为止，您已经写了一份包含一些文本、几个方程式和图像的报告。然而，它看起来仍然很朴素。您的助教还不知道您正在使用新的排版系统，您希望您的报告与其他学生的提交内容相符。在本章中，我们将看到如何使用 Typst 的样式系统格式化您的报告。

## 设置规则
正如我们在上一章中看到的，Typst 有_插入_内容的函数（例如 [`image`] 函数）和_操作_它们作为参数接收的内容的函数（例如 [`align`] 函数）。当您想要（例如）更改字体时，您可能有的第一个冲动是寻找一个执行此操作的函数并将整个文档包装在其中。

```example
#text(font: "New Computer Modern")[
  = 背景
  在冰川的情况下，流体动力学
  原理可以用来理解冰的运动
  和行为如何受到温度、压力
  和其他流体（如水）存在等
  因素的影响。
]
```

等等，函数的所有参数不应该在括号内指定吗？为什么在括号_之后_还有第二组带有内容的方括号？答案是，由于向函数传递内容在 Typst 中是如此常见的事情，因此有特殊的语法：您可以直接在普通参数后的方括号中编写内容，而不是将内容放在参数列表内，从而节省标点符号。

如上所示，这是有效的。使用 [`text`] 函数，我们可以调整其中所有文本的字体。然而，将文档包装在无数函数中并有选择地和就地应用样式很快就会变得麻烦。

幸运的是，Typst 有一个更优雅的解决方案。使用_设置规则_，您可以将样式属性应用于某种内容的所有出现。您通过输入 `{set}` 关键字，后跟要设置其属性的函数名称，以及括号中的参数列表来编写设置规则。

```example
#set text(
  font: "New Computer Modern"
)

= 背景
在冰川的情况下，流体动力学
原理可以用来理解冰的运动
和行为如何受到温度、压力
和其他流体（如水）存在等
因素的影响。
```

<div class="info-box">

想要从更技术的角度了解这里发生了什么吗？

设置规则可以概念化为为函数的某些参数设置默认值，用于该函数的所有未来使用。
</div>

## 自动补全面板 { #autocomplete }
如果您跟随操作并在应用程序中尝试了一些东西，您可能已经注意到，每当您输入 `#` 字符后，就会弹出一个面板向您显示可用的函数，并且在参数列表中显示可用的参数。这就是自动补全面板。在您编写文档时它可能非常有用：您可以通过按回车键应用其建议，或使用箭头键导航到所需的补全。可以通过按 Escape 键关闭面板，并通过输入 `#` 或按 <kbd>Ctrl</kbd> + <kbd>Space</kbd> 再次打开。使用自动补全面板发现函数的正确参数。大多数建议都带有它们功能的简短描述。

![自动补全面板](2-formatting-autocomplete.png)

## 设置页面 { #page-setup }
回到设置规则：编写规则时，您根据要样式化的元素类型选择函数。以下是一些在设置规则中常用的函数列表：

- [`text`] 设置字体系列、大小、颜色和文本的其他属性
- [`page`] 设置页面大小、边距、页眉、启用列和页脚
- [`par`] 对齐段落、设置行间距等
- [`heading`] 设置标题的外观并启用编号
- [`document`] 设置 PDF 输出中包含的元数据，如标题和作者

并非所有函数参数都可以设置。一般来说，只有告诉函数_如何_做某事的参数可以设置，而不是告诉它_用什么_做的参数。函数参考页面指示哪些参数是可设置的。

让我们为文档添加更多样式。我们想要更大的边距和衬线字体。为了示例的目的，我们还将设置另一个页面大小。

```example
#set page(
  paper: "a6",
  margin: (x: 1.8cm, y: 1.5cm),
)
#set text(
  font: "New Computer Modern",
  size: 10pt
)
#set par(
  justify: true,
  leading: 0.52em,
)

= 介绍
在这份报告中，我们将探索
影响冰川中流体动力学的
各种因素，以及它们如何
促进这些自然结构的形成和
行为。

>>> 冰川位移受多种因素影响，包括
>>> + 气候
>>> + 地形
>>> + 地质
>>>
>>> 本报告将提出冰川位移和
>>> 动力学的物理模型，并将探索
>>> 这些因素对大型冰体运动的
>>> 影响。
<<< ...

#align(center + bottom)[
  #image("glacier.jpg", width: 70%)

  *冰川构成地球气候系统的
  重要组成部分。*
]
```

这里有几个值得注意的地方。

首先是 [`page`] 设置规则。它接收两个参数：页面大小和页面边距。页面大小是一个字符串。Typst 接受[许多标准页面大小，]($page.paper)但您也可以指定自定义页面大小。边距指定为[字典。]($dictionary)字典是键值对的集合。在这种情况下，键是 `x` 和 `y`，值分别是水平和垂直边距。我们也可以通过传递带有键 `{left}`、`{right}`、`{top}` 和 `{bottom}` 的字典来为每一侧指定单独的边距。

接下来是设置 [`text`] 设置规则。在这里，我们将字体大小设置为 `{10pt}`，字体系列设置为 `{"New Computer Modern"}`。Typst 应用程序附带许多您可以为文档尝试的字体。当您在文本函数的参数列表中时，您可以在自动补全面板中发现可用的字体。

我们还设置了行间距（也称为行距）：它指定为[长度]值，我们使用 `em` 单位来指定相对于字体大小的行距：`{1em}` 等于当前字体大小（默认为 `{11pt}`）。

最后，我们通过向中心对齐添加垂直对齐来底部对齐我们的图像。垂直和水平对齐可以用 `{+}` 运算符组合以产生 2D 对齐。

## 一丝精致 { #sophistication }
为了更清楚地构建我们的文档，我们现在想要为标题编号。我们可以通过设置 [`heading`] 函数的 `numbering` 参数来做到这一点。

```example
>>> #set text(font: "New Computer Modern")
#set heading(numbering: "1.")

= 介绍
#lorem(10)

== 背景
#lorem(12)

== 方法
#lorem(15)
```

我们指定字符串 `{"1."}` 作为编号参数。这告诉 Typst 用阿拉伯数字为标题编号，并在每个级别的数字之间放一个点。我们也可以为标题使用[字母、罗马数字和符号]($numbering)：

```example
>>> #set text(font: "New Computer Modern")
#set heading(numbering: "1.a")

= 介绍
#lorem(10)

== 背景
#lorem(12)

== 方法
#lorem(15)
```

这个示例还使用 [`lorem`] 函数生成一些占位符文本。此函数接受一个数字作为参数，并生成那么多单词的 _Lorem Ipsum_ 文本。

<div class="info-box">

您是否想知道为什么标题和文本设置规则适用于所有文本和标题，即使它们不是用相应的函数产生的？

Typst 内部在您每次写 `[= 结论]` 时调用 `heading` 函数。实际上，函数调用 `[#heading[结论]]` 等同于上面的标题标记。其他标记元素的工作方式类似，它们只是相应函数调用的_语法糖_。
</div>

## 显示规则
您已经对结果非常满意了。但最后一件事需要修复：您正在写的报告是为一个更大的项目准备的，该项目的名称应该始终伴随着一个标志，即使在散文中也是如此。

您考虑您的选择。您可以使用搜索和替换在标志的每个实例之前添加 `[#image("logo.svg")]` 调用。这听起来非常繁琐。相反，您可能[定义一个自定义函数]($function/#defining-functions)，它总是产生带有其图像的标志。然而，有一个更简单的方法：

使用显示规则，您可以重新定义 Typst 如何显示某些元素。您指定 Typst 应该以不同方式显示哪些元素以及它们应该如何看起来。显示规则可以应用于文本实例、许多函数，甚至整个文档。

```example
#show "ArtosFlow": name => box[
  #box(image(
    "logo.svg",
    height: 0.7em,
  ))
  #name
]

这份报告嵌入在 ArtosFlow 项目中。
ArtosFlow 是 Artos 研究所的一个项目。
```

这个示例中有很多新语法：我们写 `{show}` 关键字，后跟我们想要以不同方式显示的文本字符串和冒号。然后，我们写一个函数，它接受应该显示的内容作为参数。在这里，我们称该参数为 `name`。我们现在可以在函数体中使用 `name` 变量来打印 ArtosFlow 名称。我们的显示规则在名称前添加标志图像，并将结果放入框中以防止标志和名称之间出现换行。图像也放在框内，这样它就不会出现在自己的段落中。

对第一个框函数和图像函数的调用不需要前导 `#`，因为它们没有直接嵌入在标记中。当 Typst 期望代码而不是标记时，不需要前导 `#` 来访问函数、关键字和变量。这可以在参数列表、函数定义和[代码块]($scripting)中观察到。

## 总结
您现在知道如何对 Typst 文档应用基本格式。您学会了如何设置字体、对齐段落、更改页面尺寸，以及使用设置规则为标题添加编号。您还学会了如何使用基本显示规则来更改文本在整个文档中的显示方式。

您已经提交了报告。您的导师对此非常满意，他们想将其改编成会议论文！在下一节中，我们将学习如何使用更高级的显示规则和函数将文档格式化为论文。
