---
description: Typst 的教程。
---

# 在 Typst 中写作
让我们开始吧！假设您被分配为大学写一份技术报告。它将包含散文、数学、标题和图表。首先，您在 Typst 应用程序上创建一个新项目。您将被带到编辑器，在那里您会看到两个面板：一个源面板，您在其中撰写文档，以及一个预览面板，您在其中看到渲染的文档。

![Typst 应用程序截图](1-writing-app.png)

您已经对报告有了一个好的角度。所以让我们从写介绍开始。在编辑器面板中输入一些文本。您会注意到文本立即出现在预览页面上。

```example
在这份报告中，我们将探索
影响冰川中流体动力学的
各种因素，以及它们如何
促进这些自然结构的形成和
行为。
```

_在整个教程中，我们将展示像这样的代码示例。就像在应用程序中一样，第一个面板包含标记，第二个面板显示预览。我们缩小了页面以适应示例，这样您就可以看到发生了什么。_

下一步是添加标题并强调一些文本。Typst 对最常见的格式化任务使用简单的标记。要添加标题，输入 `=` 字符，要用斜体强调一些文本，将其包含在 `[_下划线_]` 中。

```example
= 介绍
在这份报告中，我们将探索
影响冰川中_流体动力学_的
各种因素，以及它们如何
促进这些自然结构的形成和
行为。
```

这很容易！要添加新段落，只需在两行文本之间添加一个空行。如果该段落需要子标题，通过输入 `==` 而不是 `=` 来生成它。`=` 字符的数量决定了标题的嵌套级别。

现在我们想列出一些影响冰川动力学的情况。为此，我们使用编号列表。对于列表的每个项目，我们在行的开头输入一个 `+` 字符。Typst 将自动为项目编号。

```example
+ 气候
+ 地形
+ 地质
```

如果我们想添加项目符号列表，我们会使用 `-` 字符而不是 `+` 字符。我们也可以嵌套列表：例如，我们可以通过缩进向上面列表的第一项添加子列表。

```example
+ 气候
  - 温度
  - 降水
+ 地形
+ 地质
```

## 添加图表 { #figure }
您认为您的报告会从图表中受益。让我们添加一个。Typst 支持 PNG、JPEG、GIF、SVG 和 WebP 格式的图像。要将图像文件添加到您的项目中，首先通过单击左侧边栏中的框图标打开_文件面板_。在这里，您可以看到项目中所有文件的列表。目前，只有一个：您正在编写的主要 Typst 文件。要上传另一个文件，单击右上角带箭头的按钮。这将打开上传对话框，您可以在其中从计算机中选择要上传的文件。为您的报告选择一个图像文件。

![上传对话框](1-writing-upload.png)

我们之前看到特定符号（称为_标记_）在 Typst 中具有特定含义。我们可以分别使用 `=`、`-`、`+` 和 `_` 来创建标题、列表和强调文本。然而，为我们想要插入到文档中的每个东西都有一个特殊符号很快就会变得神秘和笨拙。因此，Typst 仅为最常见的事物保留标记符号。其他所有内容都通过_函数_插入。为了让我们的图像显示在页面上，我们使用 Typst 的 [`image`] 函数。

```example
#image("glacier.jpg")
```

一般来说，函数为一组_参数_产生一些输出。当您在标记中_调用_函数时，您提供参数，Typst 将结果（函数的_返回值_）插入到文档中。在我们的情况下，`image` 函数接受一个参数：图像文件的路径。要在标记中调用函数，我们首先需要输入 `#` 字符，紧接着是函数的名称。然后，我们将参数包含在括号中。Typst 在参数列表中识别许多不同的数据类型。

我们的文件路径是一个简短的[文本字符串]($str)，所以我们需要将其包含在双引号中。

插入的图像使用页面的整个宽度。要更改这一点，将 `width` 参数传递给 `image` 函数。这是一个_命名_参数，因此指定为 `name: value` 对。如果有多个参数，它们用逗号分隔，所以我们首先需要在路径后面放一个逗号。

```example
#image("glacier.jpg", width: 70%)
```

`width` 参数是一个[相对长度]($relative)。在我们的情况下，我们指定了一个百分比，确定图像应占用页面宽度的 `{70%}`。我们也可以指定绝对值，如 `{1cm}` 或 `{0.7in}`。

就像文本一样，图像现在默认在页面左侧对齐。它也缺少标题。让我们通过使用 [figure] 函数来解决这个问题。此函数将图表的内容作为位置参数，将可选标题作为命名参数。

在 `figure` 函数的参数列表中，Typst 已经处于代码模式。这意味着，您现在必须删除图像函数调用前的井号。井号只在直接在标记中需要（以区分文本和函数调用）。

标题由任意标记组成。要将标记提供给函数，我们将其包含在方括号中。这种构造称为_内容块_。

```example
#figure(
  image("glacier.jpg", width: 70%),
  caption: [
    _冰川_构成地球气候系统的
    重要组成部分。
  ],
)
```

您继续写报告，现在想要引用图表。为此，首先为图表附加一个标签。标签唯一标识文档中的元素。通过将某个名称包含在尖括号中，在图表后添加一个。然后，您可以通过写一个 `[@]` 符号后跟该名称在文本中引用图表。标题和方程式也可以被标记以使其可引用。

```example
如@glaciers所示的冰川，如果我们
不尽快采取行动，将不复存在！

#figure(
  image("glacier.jpg", width: 70%),
  caption: [
    _冰川_构成地球气候系统的
    重要组成部分。
  ],
) <glaciers>
```

<div class="info-box">

到目前为止，我们已经向函数传递了内容块（方括号中的标记）和字符串（双引号中的文本）。两者似乎都包含文本。有什么区别？

内容块可以包含文本，但也可以包含任何其他类型的标记、函数调用等，而字符串实际上只是一个_字符序列_，没有其他内容。

例如，图像函数期望图像文件的路径。将文本段落或另一个图像作为图像的路径参数传递是没有意义的。这就是为什么这里只允许字符串。相反，字符串在期望内容的任何地方都有效，因为文本是一种有效的内容类型。
</div>

## 添加参考文献 { #bibliography }
当您撰写报告时，您需要支持一些声明。您可以使用 [`bibliography`] 函数向文档添加参考文献。此函数期望参考文献文件的路径。

Typst 的原生参考文献格式是 [Hayagriva](https://github.com/typst/hayagriva/blob/main/docs/file-format.md)，但为了兼容性，您也可以使用 BibLaTeX 文件。由于您的同学已经做了文献调查并发送给您一个 `.bib` 文件，您将使用那个。通过文件面板上传文件以在 Typst 中访问它。

一旦文档包含参考文献，您就可以开始从中引用。引用使用与标签引用相同的语法。一旦您第一次引用来源，它就会出现在文档的参考文献部分。Typst 支持不同的引用和参考文献样式。有关更多详细信息，请查阅[参考]($bibliography.style)。

```example
= 方法
我们遵循@glacier-melt中建立的
冰川融化模型。

#bibliography("works.bib")
```

## 数学
在充实方法部分后，您转向文档的核心：您的方程式。Typst 具有内置的数学排版功能，并使用自己的数学符号。让我们从一个简单的方程式开始。我们将其包装在 `[$]` 符号中，让 Typst 知道它应该期望一个数学表达式：

```example
方程式 $Q = rho A v + C$
定义了冰川流速。
```

方程式是内联排版的，与周围文本在同一行。如果您希望它单独成行，您应该在其开始和结束处插入一个空格：

```example
冰川的流速由以下方程式定义：

$ Q = rho A v + C $
```

我们可以看到 Typst 按原样显示单个字母 `Q`、`A`、`v` 和 `C`，而将 `rho` 翻译成希腊字母。数学模式总是逐字显示单个字母。然而，多个字母被解释为符号、变量或函数名。要暗示单个字母之间的乘法，在它们之间放置空格。

如果您想要一个由多个字母组成的变量，您可以将其包含在引号中：

```example
冰川的流速由以下方程式给出：

$ Q = rho A v + "time offset" $
```

您的论文中还需要一个求和公式。我们可以使用 `sum` 符号，然后在下标和上标中指定求和范围：

```example
冰川流动造成的土壤总位移：

$ 7.32 beta +
  sum_(i=0)^nabla Q_i / 2 $
```

要为符号或变量添加下标，输入 `_` 字符，然后是下标。类似地，使用 `^` 字符表示上标。如果您的下标或上标由多个内容组成，您必须将它们包含在圆括号中。

上面的示例还向我们展示了如何插入分数：只需在分子和分母之间放置一个 `/` 字符，Typst 将自动将其转换为分数。括号被智能解析，因此您可以像在计算器中一样输入表达式，Typst 将用适当的符号替换括号内的子表达式。

```example
冰川流动造成的土壤总位移：

$ 7.32 beta +
  sum_(i=0)^nabla
    (Q_i (a_i - epsilon)) / 2 $
```

并非所有数学构造都有特殊语法。相反，我们使用函数，就像我们之前看到的 `image` 函数一样。例如，要插入列向量，我们可以使用 [`vec`]($math.vec) 函数。在数学模式中，函数调用不需要以 `#` 字符开头。

```example
$ v := vec(x_1, x_2, x_3) $
```

一些函数仅在数学模式中可用。例如，[`cal`]($math.cal) 函数用于排版通常用于集合的花体字母。[参考的数学部分]($category/math)提供了数学模式可用的所有函数的完整列表。

还有一件事：许多符号，如箭头，有很多变体。您可以通过在符号名称后附加点和修饰符名称来选择这些变体：

```example
$ a arrow.squiggly b $
```

这种符号在标记模式中也可用，但符号名称必须以 `#sym.` 开头。有关所有可用符号的列表，请参阅[符号部分]($category/symbols/sym)。

## 总结
您现在已经看到了如何在 Typst 中编写基本文档。您学会了如何强调文本、编写列表、插入图像、对齐内容和排版数学表达式。您还了解了 Typst 的函数。Typst 允许您插入到文档中的内容还有很多种，如[表格]($table)、[形状]($category/visualize)和[代码块]($raw)。您可以浏览[参考]了解这些和其他功能的更多信息。

目前，您已经完成了报告的编写。您已经通过单击右上角的下载按钮保存了 PDF。但是，您认为报告看起来可能有点太朴素了。在下一节中，我们将学习如何自定义文档的外观。
