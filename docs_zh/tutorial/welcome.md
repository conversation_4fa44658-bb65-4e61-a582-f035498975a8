---
description: Typst 的教程。
---

# 教程
欢迎来到 Typst 的教程！在本教程中，您将学习如何在 Typst 中编写和格式化文档。我们将从日常任务开始，逐步介绍更高级的功能。本教程不假设您对 Typst、其他标记语言或编程有任何先验知识。我们确实假设您知道如何编辑文本文件。

最好的开始方式是免费注册 Typst 应用程序并按照以下步骤操作。该应用程序为您提供即时预览、语法高亮和有用的自动补全功能。或者，您可以使用[开源 CLI](https://github.com/typst/typst)在本地文本编辑器中跟随操作。

## 何时使用 Typst { #when-typst }
在我们开始之前，让我们了解一下什么是 Typst 以及何时使用它。Typst 是一种用于排版文档的标记语言。它被设计为易于学习、快速且多功能。Typst 接受包含标记的文本文件并输出 PDF。

Typst 是编写任何长篇文本的好选择，如论文、文章、科学论文、书籍、报告和作业。此外，Typst 非常适合包含数学符号的任何文档，如数学、物理和工程领域的论文。最后，由于其强大的样式和自动化功能，它是任何共享通用样式的文档集的绝佳选择，如书籍系列。

## 您将学到什么 { #learnings }
本教程有四个章节。每个章节都建立在前一个章节的基础上。以下是您将在每个章节中学到的内容：

1. [在 Typst 中写作:]($tutorial/writing-in-typst) 学习如何编写文本并插入图像、方程式和其他元素。
2. [格式化:]($tutorial/formatting) 学习如何调整文档的格式，包括字体大小、标题样式等。
3. [高级样式:]($tutorial/advanced-styling) 为科学论文创建复杂的页面布局，包括作者列表和嵌入式标题等排版功能。
4. [制作模板:]($tutorial/making-a-template) 从您在上一章中创建的论文构建可重用的模板。

我们希望您会喜欢 Typst！
