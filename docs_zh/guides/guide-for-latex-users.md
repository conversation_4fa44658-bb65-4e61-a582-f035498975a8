---
description: |
  您是 LaTeX 用户吗？本指南解释了 Typst 和 LaTeX 之间的差异和
  相似之处，以便您可以快速入门。
---

# LaTeX 用户指南 { # }
如果您之前使用过 LaTeX 并想尝试 Typst，这个页面是一个很好的起点。我们将从用户角度探索这两个系统之间的主要差异。虽然 Typst 不是基于 LaTeX 构建的，并且具有不同的语法，但您将学习如何使用您的 LaTeX 技能来获得先机。

就像 LaTeX 一样，Typst 是一个基于标记的排版系统：您在文本文件中撰写文档，并用命令和其他语法对其进行标记。然后，您使用编译器将源文件排版为 PDF。但是，Typst 在几个方面也与 LaTeX 不同：首先，Typst 对常见任务使用更专用的语法（就像您可能从 Markdown 中了解的那样）。Typst 的命令也更有原则：它们都以相同的方式工作，所以与 LaTeX 不同，您只需要理解几个一般概念，而不是为每个包学习不同的约定。此外，Typst 编译比 LaTeX 更快：编译通常需要毫秒，而不是秒，因此 Web 应用程序和编译器都可以提供即时预览。

在下面，我们将涵盖从 LaTeX 切换的用户在 Typst 中撰写文档时会遇到的一些最常见问题。如果您更喜欢 Typst 的分步介绍，请查看我们的[教程]。

## 安装
您有两种使用 Typst 的方式：在[我们的 Web 应用程序](https://typst.app/signup/)中或通过[在您的计算机上安装编译器](https://github.com/typst/typst/releases)。当您使用 Web 应用程序时，我们提供一个包含电池的协作编辑器，并在您的浏览器中运行 Typst，无需安装。

如果您选择在计算机上使用 Typst，您可以将编译器下载为单个小型二进制文件，任何用户都可以运行，无需 root 权限。与 TeX Live 等流行的 LaTeX 发行版不同，包在您首次使用时下载，然后在本地缓存，保持您的 Typst 安装精简。您可以使用自己的编辑器并决定使用本地编译器在哪里存储文件。

## 如何创建新的空文档？ { #getting-started }
这很容易。您只需创建一个新的空文本文件（文件扩展名是 `.typ`）。无需样板代码即可开始。只需开始编写您的文本。它将设置在空的 A4 大小的页面上。如果您使用 Web 应用程序，请单击"+ 空文档"以创建一个带有文件的新项目并进入编辑器。[段落分隔]($parbreak)的工作方式与 LaTeX 中的相同，只需使用空行。

```example
嘿！
