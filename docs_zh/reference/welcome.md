---
description: |
  Typst 参考是 Typst 排版语言的系统性和全面性指南。
---

# 参考
此参考文档是 Typst 所有语法、概念、类型和函数的全面指南。如果您完全不熟悉 Typst，我们建议从[教程]开始，然后回到参考文档，根据需要了解更多关于 Typst 功能的信息。

## 语言
参考从语言部分开始，概述了 [Typst 的语法]($syntax)，并包含有关[样式化文档]($styling)所涉及概念的信息，使用 [Typst 的脚本功能。]($scripting)

## 函数
第二部分包括关于在 Typst 文档中用于插入、样式化、转换和布局内容的所有函数的章节。每个函数都有其目的描述、参数列表以及如何使用它的示例。

参考的最后部分解释了在 Typst 代码模式中用于操作和转换数据的所有函数。就像在前一部分中一样，每个函数都有其目的描述、参数列表以及如何使用它的示例。
