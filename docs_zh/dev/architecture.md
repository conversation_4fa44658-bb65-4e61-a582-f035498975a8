# Typst 编译器架构
想知道如何贡献或只是好奇 Typst 是如何工作的？本文档涵盖了 Typst 编译器的一般结构和架构，让您了解什么在哪里以及一切是如何组合在一起的。

## 目录
让我们从这个仓库中目录的广泛概述开始：

- `crates/typst`: 主编译器 crate，定义了完整的语言和库。
- `crates/typst-cli`: Typst 的命令行界面。这是编译器和导出器之上相对较小的层。
- `crates/typst-eval`: Typst 语言的解释器。
- `crates/typst-html`: HTML 导出器。
- `crates/typst-ide`: 暴露 IDE 功能。
- `crates/typst-kit`: 包含在 `typst-cli` 中使用的功能的各种默认实现。
- `crates/typst-layout`: Typst 的布局引擎。
- `crates/typst-library`: Typst 的标准库。
- `crates/typst-macros`: 编译器的过程宏。
- `crates/typst-pdf`: PDF 导出器。
- `crates/typst-realize`: Typst 的实现子系统。
- `crates/typst-render`: Typst 框架的渲染器。
- `crates/typst-svg`: SVG 导出器。
- `crates/typst-syntax`: 解析器和语法树定义的家。
- `crates/typst-timing`: Typst 的性能计时。
- `crates/typst-utils`: Typst 的实用程序。
- `docs`: 从 markdown 文件和内联生成官方[文档][docs]的内容
