name: 📖 Documentation
description: For issues with the documentation
title: Write a short and descriptive title!
labels:
  - docs
body:
  - type: markdown
    attributes:
      value: Thanks for reporting an issue with <PERSON><PERSON><PERSON>'s documentation. Did you [take a look](https://github.com/typst/typst/issues?q=is%3Aopen+is%3Aissue+label%3Adocs) if your issue has already been filed? If so, join the discussion there! Is your issue related to <PERSON><PERSON><PERSON>'s web app? Please file it [here](https://github.com/typst/webapp-issues/issues) instead.
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Please describe your issue.
      placeholder: Terse and specific description of the issue. You can add a screenshot by dragging an image here.
    validations:
      required: true
