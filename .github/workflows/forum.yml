name: Move to Typst Forum
on:
  issues:
    types:
      - labeled
jobs:
  add-comment:
    if: github.event.label.name == 'to-forum'
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - name: Call Discourse API to create forum post
        env:
          ISSUE_BODY: ${{ github.event.issue.body }}
          ISSUE_TITLE: ${{ github.event.issue.title }}
          ISSUE_NUMBER: ${{ github.event.issue.number }}
          ISSUE_URL: ${{ github.event.issue.html_url }}
          ISSUE_USER_LOGIN: ${{ github.event.issue.user.login }}
          REPOSITORY_OWNER: ${{ github.repository_owner }}
        run: |
          read -d '' RAW << END_OF_BODY
          This topic was moved from GitHub issue ${ISSUE_NUMBER}. Please continue the conversation here.

          Here is the original issue:
          [quote="${ISSUE_USER_LOGIN}"]
          ${ISSUE_BODY}
          [/quote]

          Browse the previous discussion at ${ISSUE_URL}
          END_OF_BODY

          TITLE_JSON=$(jq -n --arg title "[#${ISSUE_NUMBER}] ${ISSUE_TITLE}" '$title')
          RAW_JSON=$(jq -n --arg raw "$RAW" '$raw')
          EXTERNAL_ID_JSON=$(jq -n --arg external_id "gh-${REPOSITORY_OWNER}-typst-${ISSUE_NUMBER}" '$external_id')

          RESPONSE=$(curl -X POST "https://forum.typst.app/posts.json" \
            --fail-with-body \
            -H "Api-Key: ${{ secrets.DISCOURSE_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d "{
              \"title\": $TITLE_JSON,
              \"category\": 4,
              \"external_id\": $EXTERNAL_ID_JSON,
              \"raw\": $RAW_JSON
            }")

          # Check if response contains errors
          if [ $(jq -n --argjson response "$RESPONSE" '$response.errors | length') -gt 0 ]; then
            # Join the errors with commas
            ERRORS=$(jq -n --argjson response "$RESPONSE" '$response.errors | join(", ")')
            echo "DISCOURSE_ERROR=$ERRORS" >> $GITHUB_ENV
            exit 1
          fi

          # Check if the response returned a non-200 status code
          if [ $? -ne 0 ]; then
            echo "DISCOURSE_ERROR=Failed to call the Discourse API" >> $GITHUB_ENV
            exit 1
          fi

          THREAD_ID="$(jq -n --argjson response "$RESPONSE" '$response.topic_id')"
          if [ "$THREAD_ID" = 'null' ]; then
            echo "DISCOURSE_ERROR=Failed to retrieve new thread ID" >> $GITHUB_ENV
            exit 1
          fi

          echo "THREAD_ID=$THREAD_ID" >> $GITHUB_ENV
      - name: Add a comment if the workflow failed
        if: failure()
        run: |
          gh issue comment ${{ github.event.issue.number }} --body "Failed to move this issue to the Typst Forum. This is the error that the API call returned: $DISCOURSE_ERROR"
      - name: Add comment and close as not planned
        run: |
          gh issue close "$NUMBER" --reason "not planned" --comment "$BODY"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
          NUMBER: ${{ github.event.issue.number }}
          BODY: >
            We moved this issue to the Typst Forum. Please continue the discussion there: https://forum.typst.app/t/${{ env.THREAD_ID }}
