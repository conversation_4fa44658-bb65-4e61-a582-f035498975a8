name: Continuous integration
on: [push, pull_request, merge_group]

env:
  RUSTFLAGS: "-Dwarnings"
  RUSTDOCFLAGS: "-Dwarnings"
  TYPST_TESTS_EXTENDED: true
  PKG_CONFIG_i686-unknown-linux-gnu: /usr/bin/i686-linux-gnu-pkgconf

jobs:
  # This allows us to have one branch protection rule for the full test matrix.
  # See: https://github.com/orgs/community/discussions/4324
  tests:
    name: Tests
    runs-on: ubuntu-latest
    needs: [test-matrix]
    if: always()
    steps:
      - name: Tests successful
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Tests failing
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1

  test-matrix:
    name: Tests
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        bits: [64]
        include:
          - os: ubuntu-latest
            bits: 32
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - if: startsWith(matrix.os, 'ubuntu-') && matrix.bits == 32
        run: |
          sudo dpkg --add-architecture i386
          sudo apt update
          sudo apt install -y gcc-multilib libssl-dev:i386 pkg-config:i386
      - uses: dtolnay/rust-toolchain@1.88.0
        with:
          targets: ${{ matrix.bits == 32 && 'i686-unknown-linux-gnu' || '' }}
      - uses: Swatinem/rust-cache@v2
        with:
          key: ${{ matrix.bits }}
      - run: cargo test --workspace --no-run ${{ matrix.bits == 32 && '--target i686-unknown-linux-gnu' || '' }}
      - run: cargo test --workspace --no-fail-fast ${{ matrix.bits == 32 && '--target i686-unknown-linux-gnu' || '' }}
      - name: Upload rendered test output
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: tests-rendered-${{ matrix.os }}-${{ matrix.bits }}
          path: tests/store/render/**
          retention-days: 3
      - name: Update test artifacts
        if: failure()
        run: |
          cargo test --workspace --test tests ${{ matrix.bits == 32 && '--target i686-unknown-linux-gnu' || '' }} -- --update
          echo 'updated_artifacts=1' >> "$GITHUB_ENV"
      - name: Upload updated reference output (for use if the test changes are desired)
        if: failure() && env.updated_artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tests-updated-${{ matrix.os }}-${{ matrix.bits }}
          path: tests/ref/**
          retention-days: 3

  checks:
    name: Check clippy, formatting, and documentation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@1.88.0
        with:
          components: clippy, rustfmt
      - uses: Swatinem/rust-cache@v2
      - run: cargo clippy --workspace --all-targets --all-features
      - run: cargo clippy --workspace --all-targets --no-default-features
      - run: cargo fmt --check --all
      - run: cargo doc --workspace --no-deps
      - run: git diff --exit-code

  min-version:
    name: Check minimum Rust version
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@1.88.0
      - uses: Swatinem/rust-cache@v2
      - run: cargo check --workspace

  fuzz:
    name: Check fuzzers
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2025-05-10
      - uses: Swatinem/rust-cache@v2
      - run: cargo install --locked cargo-fuzz@0.12.0
      - run: cd tests/fuzz && cargo fuzz build --dev

  miri:
    name: Check unsafe code
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          components: miri
          toolchain: nightly-2025-05-10
      - uses: Swatinem/rust-cache@v2
      - run: cargo miri test -p typst-library test_miri
