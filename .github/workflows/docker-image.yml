name: Build and Publish Docker Image

on:
  release:
    types: [published]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io

jobs:
  build:
    name: Build Images
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        platform:
          - linux/amd64
          - linux/arm64
    permissions:
      contents: read
      packages: write

    steps:
      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
          echo "IMAGE_NAME=${REGISTRY}/${GITHUB_REPOSITORY@L}" >> $GITHUB_ENV

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get current date
        run: echo "TYPST_BUILD_DATE=\"$(date -u +'%Y-%m-%dT%H:%M:%SZ')\" >> $GITHUB_ENV"

      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v3.7.1
        with:
          platforms: ${{ matrix.platform }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5.5.1
        with:
          images: ${{ env.IMAGE_NAME }}

      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@v3.3.0
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker image
        id: build
        uses: docker/build-push-action@v6.9.0
        with:
          labels: ${{ steps.meta.outputs.labels }}
          platforms: ${{ matrix.platform }}
          outputs: type=image,name=${{ env.IMAGE_NAME }},push-by-digest=true,name-canonical=true,push=true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            REVISION=${{ github.sha }}
            CREATED=${{ env.TYPST_BUILD_DATE }}

      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

  merge:
    name: Merge and Publish
    runs-on: ubuntu-latest
    needs:
      - build

    permissions:
      contents: read
      packages: write

    steps:
      - name: Prepare
        run: |
          echo "IMAGE_NAME=${REGISTRY}/${GITHUB_REPOSITORY@L}" >> $GITHUB_ENV

      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}

      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@v3.0.0
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create manifest list and push
        working-directory: /tmp/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.IMAGE_NAME }}@sha256:%s ' *)
      - name: Inspect image
        run: |
          docker buildx imagetools inspect ${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}
