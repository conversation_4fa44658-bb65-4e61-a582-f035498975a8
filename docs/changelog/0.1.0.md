---
title: 0.1.0
description: Changes in Typst 0.1.0
---

# Version 0.1.0 (April 04, 2023)

## Breaking changes
- When using the CLI, you now have to use subcommands:
  - `typst compile file.typ` or `typst c file.typ` to create a PDF
  - `typst watch file.typ` or `typst w file.typ` to compile and watch
  - `typst fonts` to list all fonts
- Manual counters now start at zero. Read the "How to step" section
  [here]($counter) for more details
- The [bibliography styles]($bibliography.style) `{"author-date"}` and
  `{"author-title"}` were renamed to `{"chicago-author-date"}` and
  `{"chicago-author-title"}`

## Figure improvements
- Figures now automatically detect their content and adapt their behavior.
  Figures containing tables, for instance, are automatically prefixed with
  "Table X" and have a separate counter
- The figure's supplement (e.g. "Figure" or "Table") can now be customized
- In addition, figures can now be completely customized because the show rule
  gives access to the automatically resolved kind, supplement, and counter

## Bibliography improvements
- The [`bibliography`] now also accepts multiple bibliography paths (as an
  array)
- Parsing of BibLaTeX files is now more permissive (accepts non-numeric edition,
  pages, volumes, dates, and Jabref-style comments; fixed abbreviation parsing)
- Labels and references can now include `:` and `.` except at the end
- Fixed APA bibliography ordering

## Drawing additions
- Added [`polygon`] function for drawing polygons
- Added support for clipping in [boxes]($box.clip) and [blocks]($block.clip)

## Command line interface
- Now returns with non-zero status code if there is an error
- Now watches the root directory instead of the current one
- Now puts the PDF file next to input file by default
- Now accepts more kinds of input files (e.g. `/dev/stdin`)
- Added `--open` flag to directly open the PDF

## Miscellaneous improvements
- Added [`yaml`] function to load data from YAML files
- Added basic i18n for a few more languages (IT, RU, ZH, FR, PT)
- Added numbering support for Hebrew
- Added support for [integers]($int) with base 2, 8, and 16
- Added symbols for double bracket and laplace operator
- The [`link`] function now accepts [labels]($label)
- The link syntax now allows more characters
- Improved justification of Japanese and Chinese text
- Calculation functions behave more consistently w.r.t to non-real results
- Replaced deprecated angle brackets
- Reduced maximum function call depth from 256 to 64
- Fixed [`first-line-indent`]($par.first-line-indent) being not applied when a
  paragraph starts with styled text
- Fixed extraneous spacing in unary operators in equations
- Fixed block spacing, e.g. in `{block(above: 1cm, below: 1cm, ..)}`
- Fixed styling of text operators in math
- Fixed invalid parsing of language tag in raw block with a single backtick
- Fixed bugs with displaying counters and state
- Fixed crash related to page counter
- Fixed crash when [`symbol`] function was called without arguments
- Fixed crash in bibliography generation
- Fixed access to label of certain content elements
- Fixed line number in error message for CSV parsing
- Fixed invalid autocompletion after certain markup elements

## Contributors
<contributors from="v23-03-28" to="v0.1.0" />
