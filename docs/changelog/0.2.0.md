---
title: 0.2.0
description: Changes in Typst 0.2.0
---

# Version 0.2.0 (April 11, 2023)

## Breaking changes
- Removed support for iterating over index and value in
  [for loops]($scripting/#loops). This is now handled via unpacking and
  enumerating. Same goes for the [`map`]($array.map) method.
- [Dictionaries]($dictionary) now iterate in insertion order instead of
  alphabetical order.

## New features
- Added [unpacking syntax]($scripting/#bindings) for let bindings, which allows
  things like `{let (1, 2) = array}`
- Added [`enumerate`]($array.enumerate) method
- Added [`path`] function for drawing Bézier paths
- Added [`layout`] function to access the size of the surrounding page or
  container
- Added `key` parameter to [`sorted`]($array.sorted) method

## Command line interface
- Fixed `--open` flag blocking the program
- New Computer Modern font is now embedded into the binary
- Shell completions and man pages can now be generated by setting the
  `GEN_ARTIFACTS` environment variable to a target directory and then building
  Typst

## Miscellaneous improvements
- Fixed page numbering in outline
- Added basic i18n for a few more languages (AR, NB, CS, NN, PL, SL, ES, UA, VI)
- Added a few numbering patterns (Ihora, Chinese)
- Added `sinc` [operator]($math.op)
- Fixed bug where math could not be hidden with [`hide`]
- Fixed sizing issues with box, block, and shapes
- Fixed some translations
- Fixed inversion of "R" in [`cal`]($math.cal) and [`frak`]($math.frak) styles
- Fixed some styling issues in math
- Fixed supplements of references to headings
- Fixed syntax highlighting of identifiers in certain scenarios
- [Ratios]($ratio) can now be multiplied with more types and be converted to
  [floats]($float) with the [`float`] function

## Contributors
<contributors from="v0.1.0" to="v0.2.0" />
