---
title: 0.11.0
description: Changes in Typst 0.11.0
---

# Version 0.11.0 (March 15, 2024)

## Tables
- Tables are now _much_ more flexible, read the new
  [table guide]($guides/table-guide) to get started
- Added [`table.cell`] element for per-cell configuration
- Cells can now span multiple [columns]($table.cell.colspan) or
  [rows]($table.cell.rowspan)
- The [stroke]($table.cell.stroke) of individual cells can now be customized
- The [`align`]($table.align) and [`inset`]($table.inset) arguments of the table
  function now also take `{(x, y) => ..}` functions
- Added [`table.hline`] and [`table.vline`] for convenient line customization
- Added [`table.header`] element for table headers that repeat on every page
- Added [`table.footer`] element for table footers that repeat on every page
- All the new table functionality is also available for [grids]($grid)
- Fixed gutter-related bugs

_Thanks to [@PgBiel](https://github.com/PgBiel) for his work on tables!_

## Templates
- You can now use template packages to get started with new projects. Click
  _Start from template_ on the web app's dashboard and choose your preferred
  template or run the `typst init <template>` command in the CLI. You can
  [browse the available templates here]($universe/search/?kind=templates).
- Switching templates after the fact has become easier. You can just import a
  styling function from a different template package.
- Package authors can now submit their own templates to the
  [package repository](https://github.com/typst/packages). Share a template
  for a paper, your institution, or an original work to help the community get
  a head start on their projects.
- Templates and packages are now organized by category and discipline. Filter
  packages by either taxonomy in the _Start from template_ wizard. If you are a
  package author, take a look at the new documentation for
  [categories](https://github.com/typst/packages/blob/main/CATEGORIES.md) and
  [disciplines](https://github.com/typst/packages/blob/main/DISCIPLINES.md).

## Context
- Added _context expressions:_ Read the chapter on [context] to get started
- With context, you can access settable properties, e.g. `{context text.lang}`
  to access the language set via `{set text(lang: "..")}`
- The following existing functions have been made contextual: [`query`],
  [`locate`], [`measure`], [`counter.display`], [`counter.at`],
  [`counter.final`], [`state.at`], and [`state.final`]
- Added contextual methods [`counter.get`] and [`state.get`] to retrieve the
  value of a counter or state in the current context
- Added contextual function [`here`] to retrieve the [location] of the current
  context
- The [`locate`] function now returns the location of a selector's unique match.
  Its old behavior has been replaced by context expressions and only remains
  temporarily available for compatibility.
- The [`counter.at`] and [`state.at`] methods are now more flexible: They
  directly accept any kind of [locatable]($location/#locatable) selector with a
  unique match (e.g. a label) instead of just locations
- When context is available, [`counter.display`] now directly returns the result
  of applying the numbering instead of yielding opaque content. It should not be
  used anymore without context. (Deprecation planned)
- The `state.display` function should not be used anymore, use [`state.get`]
  instead (Deprecation planned)
- The `location` argument of [`query`], [`counter.final`], and [`state.final`]
  should not be used anymore (Deprecation planned)
- The `styles` argument of the `measure` function should not be used anymore
  (Deprecation planned)
- The `style` function should not be used anymore, use context instead
  (Deprecation planned)
- The correct context is now also provided in various other places where it is
  available, e.g. in show rules, layout callbacks, and numbering functions in
  the outline

## Styling
- Fixed priority of multiple [show-set rules]($styling/#show-rules): They now
  apply in the same order as normal set rules would
- Show-set rules on the same element (e.g. `{show heading.where(level: 1): set
  heading(numbering: "1.")}`) now work properly
- Setting properties on an element within a transformational show rule (e.g.
  `{show heading: it => { set heading(..); it }}`) is **not** supported anymore
  (previously it also only worked sometimes); use show-set rules instead
  **(Breaking change)**
- Text show rules that match their own output now work properly (e.g.
  `` {show "cmd": `cmd`} ``)
- The elements passed to show rules and returned by queries now contain all
  fields of their respective element functions rather than just specific ones
- All settable properties can now be used in [where]($function.where) selectors
- [And]($selector.and) and [or]($selector.or) selectors can now be used with
  show rules
- Errors within show rules and context expressions are now ignored in all but
  the last introspection iteration, in line with the behavior of the old
  [`locate`]
- Fixed a bug where document set rules were allowed after content

## Layout
- Added `reflow` argument to [`rotate`]($rotate) and [`scale`]($scale) which
  lets them affect the layout
- Fixed a bug where [floating placement]($place.float) or
  [floating figures]($figure.placement) could end up out of order
- Fixed overlap of text and figure for full-page floating figures
- Fixed various cases where the [`hide`] function didn't hide its contents
  properly
- Fixed usage of [`h`] and [`v`] in [stacks]($stack)
- Invisible content like a counter update will no longer force a visible block
  for just itself
- Fixed a bug with horizontal spacing followed by invisible content (like a
  counter update) directly at the start of a paragraph

## Text
- Added [`stroke`]($text.stroke) property for text
- Added basic i18n for Serbian and Catalan
- Added support for contemporary Japanese [numbering] method
- Added patches for various wrong metadata in specific fonts
- The [text direction]($text.dir) can now be overridden within a paragraph
- Fixed Danish [smart quotes]($smartquote)
- Fixed font fallback next to a line break
- Fixed width adjustment of JIS-style Japanese punctuation
- Fixed Finnish translation of "Listing"
- Fixed Z-ordering of multiple text decorations (underlines, etc.)
- Fixed a bug due to which text [features]($text.features) could not be
  overridden in consecutive set rules

## Model
- Added [`depth`]($heading.depth) and [`offset`]($heading.offset) arguments to
  heading to increase or decrease the heading level for a bunch of content; the
  heading syntax now sets `depth` rather than `level` **(Breaking change)**
- List [markers]($list.marker) now cycle by default
- The [`quote`] function now more robustly selects the correct quotes based on
  language and nesting
- Fixed indent bugs related to the default show rule of [terms]

## Math
- Inline equations now automatically linebreak at appropriate places
- Added [`number-align`]($math.equation.number-align) argument to equations
- Added support for adjusting the [`size`]($math.accent.size) of accents
  relative to their base
- Improved positioning of accents
- [Primes]($math.primes) are now always attached as [scripts]($math.scripts) by
  default
- Exposed [`math.primes`] element which backs the `[$f'$]` syntax in math
- Math mode is not affected by [`strong`] and [`emph`] anymore
- Fixed [`attach`]($math.attach) under [fractions]($math.frac)
- Fixed that [`math.class`] did not affect smart limit placement
- Fixed weak spacing in [`lr`]($math.lr) groups
- Fixed layout of large operators for Cambria Math font
- Fixed math styling of Hebrew symbol codepoints

## Symbols
- Added `gradient` as an alias for `nabla`
- Added `partial` as an alias for `diff`, `diff` will be deprecated in the
  future
- Added `colon.double`, `gt.approx`, `gt.napprox`, `lt.approx`, and `lt.napprox`
- Added `arrow.r.tilde` and `arrow.l.tilde`
- Added `tilde.dot`
- Added `forces` and `forces.not`
- Added `space.nobreak.narrow`
- Added `lrm` (Left-to-Right Mark) and `rlm` (Right-to-Left Mark)
- Fixed `star.stroked` symbol (which previously had the wrong codepoint)

## Scripting
- Arrays can now be compared lexicographically
- Added contextual method [`to-absolute`]($length.to-absolute) to lengths
- Added [`calc.root`]($calc.root)
- Added [`int.signum`] and [`float.signum`] methods
- Added [`float.is-nan`] and [`float.is-infinite`] methods
- Added [`int.bit-not`], [`int.bit-and`], [`int.bit-or`], [`int.bit-xor`],
  [`int.bit-lshift`], and [`int.bit-rshift`] methods
- Added [`array.chunks`] method
- A module can now be converted to a dictionary with the
  [dictionary constructor]($dictionary/#constructor) to access its contents
  dynamically
- Added [`row-type`]($csv.row-type) argument to `csv` function to configure
  how rows will be represented
- [XML parsing]($xml) now allows DTDs (document type definitions)
- Improved formatting of negative numbers with [`str`]($str) and [`repr`]($repr)
- For loops can now iterate over [bytes]
- Fixed a bug with pattern matching in for loops
- Fixed a bug with labels not being part of [`{.fields()}`]($content.fields)
  dictionaries
- Fixed a bug where unnamed argument sinks wouldn't capture excess arguments
- Fixed typo in `repr` output of strokes

## Syntax
- Added support for nested [destructuring patterns]($scripting/#bindings)
- Special spaces (like thin or non-breaking spaces) are now parsed literally
  instead of being collapsed into normal spaces **(Breaking change)**
- Korean text can now use emphasis syntax without adding spaces
  **(Breaking change)**
- The token [`context`] is now a keyword and cannot be used as an identifier
  anymore **(Breaking change)**
- Nested line comments aren't allowed anymore in block comments
  **(Breaking change)**
- Fixed a bug where `x.)` would be treated as a field access
- Text elements can now span across curly braces in markup
- Fixed silently wrong parsing when function name is parenthesized
- Fixed various bugs with parsing of destructuring patterns, arrays, and
  dictionaries

## Tooling & Diagnostics
- Click-to-jump now works properly within [`raw`] text
- Added suggestion for accessing a field if a method doesn't exist
- Improved hint for calling a function stored in a dictionary
- Improved errors for mutable accessor functions on arrays and dictionaries
- Fixed error message when calling constructor of type that doesn't have one
- Fixed confusing error message with nested dictionaries for strokes on
  different sides
- Fixed autocompletion for multiple packages with the same name from different
  namespaces

## Visualization
- The [`image`] function doesn't upscale images beyond their natural size
  anymore
- The [`image`] function now respects rotation stored in EXIF metadata
- Added support for SVG filters
- Added alpha component to [`luma`]($color.luma) colors
- Added [`color.transparentize`] and [`color.opacify`] methods
- Improved [`color.negate`] function
- Added [`stroke`]($highlight.stroke) and [`radius`]($highlight.radius)
  arguments to `highlight` function
- Changed default [`highlight`] color to be transparent
- CMYK to RGB conversion is now color-managed
- Fixed crash with gradients in Oklch color space
- Fixed color-mixing for hue-based spaces
- Fixed bugs with color conversion
- SVG sizes are not rounded anymore, preventing slightly wrong aspect ratios
- Fixed a few other SVG-related bugs
- [`color.components`] doesn't round anything anymore

## Export
- PDFs now contain named destinations for headings derived from their labels
- The internal PDF structure was changed to make it easier for external tools to
  extract or modify individual pages, avoiding a bug with Typst PDFs in Apple
  Preview
- PDFs produced by Typst should now be byte-by-byte reproducible when
  `{set document(date: none)}` is set
- Added missing flag to PDF annotation
- Fixed multiple bugs with gradients in PDF export
- Fixed a bug with patterns in PDF export
- Fixed a bug with embedding of grayscale images in PDF export
- Fixed a bug with To-Unicode mapping of CFF fonts in PDF export
- Fixed a bug with the generation of the PDF outline
- Fixed a sorting bug in PDF export leading to non-reproducible output
- Fixed a bug with transparent text in PNG export
- Exported SVG files now include units in their top-level `width` and `height`

## Command line interface
- Added support for passing [inputs]($category/foundations/sys) via a CLI flag
- When passing the filename `-`, Typst will now read input from stdin
- Now uses the system-native TLS implementation for network fetching which
  should be generally more robust
- Watch mode will now properly detect when a previously missing file is created
- Added `--color` flag to configure whether to print colored output
- Fixed user agent with which packages are downloaded
- Updated bundled fonts to the newest versions

## Development
- Added `--vendor-openssl` to CLI to configure whether to link OpenSSL
  statically instead of dynamically (not applicable to Windows and Apple
  platforms)
- Removed old tracing (and its verbosity) flag from the CLI
- Added new `--timings` flag which supersedes the old flamegraph profiling in
  the CLI
- Added minimal CLI to `typst-docs` crate for extracting the language and
  standard library documentation as JSON
- The `typst_pdf::export` function's `ident` argument switched from `Option` to
  `Smart`. It should only be set to `Smart::Custom` if you can provide a stable
  identifier (like the web app can). The CLI sets `Smart::Auto`.

## Contributors
<contributors from="v0.10.0" to="v0.11.0" />
