---
title: 0.5.0
description: Changes in Typst 0.5.0
---

# Version 0.5.0 (June 9, 2023)

## Text and Layout
- Added [`raw`] syntax highlighting for many more languages
- Added support for Korean [numbering]
- Added basic i18n for a few more languages (NL, SV, DA)
- Improved line breaking for East Asian languages
- Expanded functionality of outline [`indent`]($outline.indent) property
- Fixed footnotes in columns
- Fixed page breaking bugs with [footnotes]($footnote)
- Fixed bug with handling of footnotes in lists, tables, and figures
- Fixed a bug with CJK punctuation adjustment
- Fixed a crash with rounded rectangles
- Fixed alignment of [`line`] elements

## Math
- **Breaking change:** The syntax rules for mathematical
  [attachments]($math.attach) were improved: `[$f^abs(3)$]` now parses as
  `[$f^(abs(3))$]` instead of `[$(f^abs)(3)$]`. To disambiguate, add a space:
  `[$f^zeta (3)$]`.
- Added [forced size]($category/math/sizes) commands for math (e.g.,
  [`display`]($math.display))
- Added [`supplement`]($math.equation.supplement) parameter to
  [`equation`]($math.equation), used by [references]($ref)
- New [symbols]($category/symbols/sym): `bullet`, `xor`, `slash.big`,
  `sigma.alt`, `tack.r.not`, `tack.r.short`, `tack.r.double.not`
- Fixed a bug with symbols in matrices
- Fixed a crash in the [`attach`]($math.attach) function

## Scripting
- Added new [`datetime`] type and [`datetime.today`]($datetime.today) to
  retrieve the current date
- Added [`str.from-unicode`]($str.from-unicode) and
  [`str.to-unicode`]($str.to-unicode) functions
- Added [`fields`]($content.fields) method on content
- Added `base` parameter to [`str`] function
- Added [`calc.exp`]($calc.exp) and [`calc.ln`]($calc.ln)
- Improved accuracy of [`calc.pow`]($calc.pow) and [`calc.log`]($calc.log) for
  specific bases
- Fixed [removal]($dictionary.remove) order for dictionary
- Fixed `.at(default: ..)` for [strings]($str.at) and [content]($content.at)
- Fixed field access on styled elements
- Removed deprecated `calc.mod` function

## Command line interface
- Added PNG export via `typst compile source.typ output-{n}.png`. The output
  path must contain `[{n}]` if the document has multiple pages.
- Added `--diagnostic-format=short` for Unix-style short diagnostics
- Doesn't emit color codes anymore if stderr isn't a TTY
- Now sets the correct exit when invoked with a nonexistent file
- Now ignores UTF-8 BOM in Typst files

## Miscellaneous Improvements
- Improved errors for mismatched delimiters
- Improved error message for failed length comparisons
- Fixed a bug with images not showing up in Apple Preview
- Fixed multiple bugs with the PDF outline
- Fixed citations and other searchable elements in [`hide`]
- Fixed bugs with [reference supplements]($ref.supplement)
- Fixed Nix flake

## Contributors
<contributors from="v0.4.0" to="v0.5.0" />
