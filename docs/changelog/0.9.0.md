---
title: 0.9.0
description: Changes in Typst 0.9.0
---

# Version 0.9.0 (October 31, 2023)

## Bibliography management
- New bibliography engine based on [CSL](https://citationstyles.org/) (Citation
  Style Language). Ships with about 100 commonly used citation styles and can
  load custom `.csl` files.
- Added new [`form`]($cite.form) argument to the `cite` function to produce
  different forms of citations (e.g. for producing a citation suitable for
  inclusion in prose)
- The [`cite`] function now takes only a single label/key instead of allowing
  multiple. Adjacent citations are merged and formatted according to the
  citation style's rules automatically. This works both with the reference
  syntax and explicit calls to the `cite` function. **(Breaking change)**
- The `cite` function now takes a [label] instead of a string
  **(Breaking change)**
- Added [`full`]($bibliography.full) argument to bibliography function to print
  the full bibliography even if not all works were cited
- Bibliography entries can now contain Typst equations (wrapped in `[$..$]` just
  like in markup), this works both for `.yaml` and `.bib` bibliographies
- The hayagriva YAML format was improved. See its
  [changelog](https://github.com/typst/hayagriva/blob/main/CHANGELOG.md) for
  more details. **(Breaking change)**
- A few bugs with `.bib` file parsing were fixed
- Removed `brackets` argument of `cite` function in favor of `form`

## Visualization
- Gradients and colors (thanks to [@Dherse](https://github.com/Dherse))
  - Added support for [gradients]($gradient) on shapes and text
  - Supports linear, radial, and conic gradients
  - Added support for defining colors in more color spaces, including
    [Oklab]($color.oklab), [Linear RGB(A)]($color.linear-rgb),
    [HSL]($color.hsl), and [HSV]($color.hsv)
  - Added [`saturate`]($color.saturate), [`desaturate`]($color.desaturate), and
    [`rotate`]($color.rotate) functions on colors
  - Added [`color.map`]($color/#predefined-color-maps) module with predefined
    color maps that can be used with gradients
  - Rename `kind` function on colors to [`space`]($color.space)
  - Removed `to-rgba`, `to-cmyk`, and `to-luma` functions in favor of a new
    [`components`]($color.components) function
- Improved rendering of [rectangles]($rect) with corner radius and varying
  stroke widths
- Added support for properly clipping [boxes]($box.clip) and
  [blocks]($block.clip) with a border radius
- Added `background` parameter to [`overline`], [`underline`], and [`strike`]
  functions
- Fixed inaccurate color embedding in PDFs
- Fixed ICC profile handling for images embedded in PDFs

## Text and Layout
- Added support for automatically adding proper
  [spacing]($text.cjk-latin-spacing) between CJK and Latin text (enabled by
  default)
- Added support for automatic adjustment of more CJK punctuation
- Added [`quote`] element for inserting inline and block quotes with optional
  attributions
- Added [`raw.line`]($raw.line) element for customizing the display of
  individual lines of raw text, e.g. to add line numbers while keeping proper
  syntax highlighting
- Added support for per-side [inset]($table.inset) customization to table
  function
- Added Hungarian and Romanian translations
- Added support for Czech hyphenation
- Added support for setting custom [smart quotes]($smartquote)
- The default [figure separator]($figure.caption.separator) now reacts to the
  currently set language and region
- Improved line breaking of links / URLs (especially helpful for bibliographies
  with many URLs)
- Improved handling of consecutive hyphens in justification algorithm
- Fixed interaction of justification and hanging indent
- Fixed a bug with line breaking of short lines without spaces when
  justification is enabled
- Fixed font fallback for hyphen generated by hyphenation
- Fixed handling of word joiner and other no-break characters during hyphenation
- Fixed crash when hyphenating after an empty line
- Fixed line breaking of composite emoji like 🏳️‍🌈
- Fixed missing text in some SVGs
- Fixed font fallback in SVGs
- Fixed behavior of [`to`]($pagebreak.to) argument on `pagebreak` function
- Fixed `{set align(..)}` for equations
- Fixed spacing around [placed]($place) elements
- Fixed coalescing of [`above`]($block.above) and [`below`]($block.below)
  spacing if given in em units and the font sizes differ
- Fixed handling of `extent` parameter of [`underline`], [`overline`], and
  [`strike`] functions
- Fixed crash for [floating placed elements]($place.float) with no specified
  vertical alignment
- Partially fixed a bug with citations in footnotes

## Math
- Added `gap` argument for [`vec`]($math.vec.gap), [`mat`]($math.mat.gap), and
  [`cases`]($math.cases.gap) function
- Added `size` argument for [`abs`]($math.abs), [`norm`]($math.norm),
  [`floor`]($math.floor), [`ceil`]($math.ceil), and [`round`]($math.round)
  functions
- Added [`reverse`]($math.cases.reverse) parameter to cases function
- Added support for multinomial coefficients to [`binom`]($math.binom) function
- Removed `rotation` argument on [`cancel`]($math.cancel) function in favor of a
  new and more flexible `angle` argument **(Breaking change)**
- Added `wide` constant, which inserts twice the spacing of `quad`
- Added `csch` and `sech` [operators]($math.op)
- `↼`, `⇀`, `↔`, and `⟷` can now be used as [accents]($math.accent)
- Added `integral.dash`, `integral.dash.double`, and `integral.slash`
  [symbols]($category/symbols/sym)
- Added support for specifying negative indices for
  [augmentation]($math.mat.augment) lines to position the line from the back
- Fixed default color of matrix [augmentation]($math.mat.augment) lines
- Fixed attachment of primes to inline expressions
- Math content now respects the text [baseline]($text.baseline) setting

## Performance
- Fixed a bug related to show rules in templates which would effectively disable
  incremental compilation in affected documents
- Micro-optimized code in several hot paths, which brings substantial
  performance gains, in particular in incremental compilations
- Improved incremental parsing, which affects the whole incremental compilation
  pipeline
- Added support for incremental parsing in the CLI
- Added support for incremental SVG encoding during PDF export, which greatly
  improves export performance for documents with many SVG

## Tooling and Diagnostics
- Improved autocompletion for variables that are in-scope
- Added autocompletion for package imports
- Added autocompletion for [labels]($label)
- Added tooltip that shows which variables a function captures (when hovering
  over the equals sign or arrow of the function)
- Diagnostics are now deduplicated
- Improved diagnostics when trying to apply unary `+` or `-` to types that only
  support binary `+` and `-`
- Error messages now state which label or citation key isn't present in the
  document or its bibliography
- Fixed a bug where function argument parsing errors were shadowed by function
  execution errors (e.g. when trying to call [`array.sorted`]($array.sorted) and
  passing the key function as a positional argument instead of a named one).

## Export
- Added support for configuring the document's creation
  [`date`]($document.date). If the `date` is set to `{auto}` (the default), the
  PDF's creation date will be set to the current date and time.
- Added support for configuring document [`keywords`]($document.keywords)
- Generated PDFs now contain PDF document IDs
- The PDF creator tool metadata now includes the Typst version

## Web app
- Added version picker to pin a project to an older compiler version
  (with support for Typst 0.6.0+)
- Fixed desyncs between editor and compiler and improved overall stability
- The app now continues to highlight the document when typing while the document
  is being compiled

## Command line interface
- Added support for discovering fonts through fontconfig
- Now clears the screen instead of resetting the terminal
- Now automatically picks correct file extension for selected output format
- Now only regenerates images for changed pages when using `typst watch` with
  PNG or SVG export

## Miscellaneous Improvements
- Added [`version`] type and `sys.version` constant specifying the current
  compiler version. Can be used to gracefully support multiple versions.
- The U+2212 MINUS SIGN is now used when displaying a numeric value, in the
  [`repr`] of any numeric value and to replace a normal hyphen in text mode when
  before a digit. This improves, in particular, how negative integer values are
  displayed in math mode.
- Added support for specifying a default value instead of failing for `remove`
  function in [array]($array.remove) and [dictionary]($dictionary.remove)
- Simplified page setup guide examples
- Switched the documentation from using the word "hashtag" to the word "hash"
  where appropriate
- Added support for [`array.zip`]($array.zip) without any further arguments
- Fixed crash when a plugin tried to read out of bounds memory
- Fixed crashes when handling infinite [lengths]($length)
- Fixed introspection (mostly bibliography) bugs due to weak page break close to
  the end of the document

## Development
- Extracted `typst::ide` into separate `typst_ide` crate
- Removed a few remaining `'static` bounds on `&dyn World`
- Removed unnecessary dependency, which reduces the binary size
- Fixed compilation of `typst` by itself (without `typst-library`)
- Fixed warnings with Nix flake when using `lib.getExe`

## Contributors
<contributors from="v0.8.0" to="v0.9.0" />
