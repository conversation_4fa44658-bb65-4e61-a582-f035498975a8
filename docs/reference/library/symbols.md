These two modules give names to symbols and emoji to make them easy to insert
with a normal keyboard. Alternatively, you can also always directly enter
Unicode symbols into your text and formulas. In addition to the symbols listed
below, math mode defines `dif` and `Dif`. These are not normal symbol values
because they also affect spacing and font style.

You can define custom symbols with the constructor function of the
[symbol]($symbol) type.
