# This is responsible for the fact that certain math functions are grouped
# together into one documentation page although they are not part of any scope.

- name: variants
  title: Variants
  category: math
  path: ["math"]
  filter: ["serif", "sans", "frak", "mono", "bb", "cal", "scr"]
  details: |
    Alternate typefaces within formulas.

    These functions are distinct from the [`text`] function because math fonts
    contain multiple variants of each letter.

- name: styles
  title: Styles
  category: math
  path: ["math"]
  filter: ["upright", "italic", "bold"]
  details: |
    Alternate letterforms within formulas.

    These functions are distinct from the [`text`] function because math fonts
    contain multiple variants of each letter.

- name: sizes
  title: Sizes
  category: math
  path: ["math"]
  filter: ["display", "inline", "script", "sscript"]
  details: |
    Forced size styles for expressions within formulas.

    These functions allow manual configuration of the size of equation elements
    to make them look as in a display/inline equation or as if used in a root or
    sub/superscripts.

- name: underover
  title: Under/Over
  category: math
  path: ["math"]
  filter: [
    "underline",
    "overline",
    "underbrace",
    "overbrace",
    "underbracket",
    "overbracket",
    "underparen",
    "overparen",
    "undershell",
    "overshell",
  ]
  details: |
    Delimiters above or below parts of an equation.

    The braces and brackets further allow you to add an optional annotation
    below or above themselves.

- name: roots
  title: Roots
  category: math
  path: ["math"]
  filter: ["root", "sqrt"]
  details: |
    Square and non-square roots.

    # Example
    ```example
    $ sqrt(3 - 2 sqrt(2)) = sqrt(2) - 1 $
    $ root(3, x) $
    ```

- name: attach
  title: Attach
  category: math
  path: ["math"]
  filter: ["attach", "scripts", "limits"]
  details: |
    Subscript, superscripts, and limits.

    Attachments can be displayed either as sub/superscripts, or limits. Typst
    automatically decides which is more suitable depending on the base, but you
    can also control this manually with the `scripts` and `limits` functions.

    If you want the base to stretch to fit long top and bottom attachments (for
    example, an arrow with text above it), use the [`stretch`]($math.stretch)
    function.

    # Example
    ```example
    $ sum_(i=0)^n a_i = 2^(1+i) $
    ```

    # Syntax
    This function also has dedicated syntax for attachments after the base: Use
    the underscore (`_`) to indicate a subscript i.e. bottom attachment and the
    hat (`^`) to indicate a superscript i.e. top attachment.

- name: lr
  title: Left/Right
  category: math
  path: ["math"]
  filter: ["lr", "mid", "abs", "norm", "floor", "ceil", "round"]
  details: |
    Delimiter matching.

    The `lr` function allows you to match two delimiters and scale them with the
    content they contain. While this also happens automatically for delimiters
    that match syntactically, `lr` allows you to match two arbitrary delimiters
    and control their size exactly. Apart from the `lr` function, Typst provides
    a few more functions that create delimiter pairings for absolute, ceiled,
    and floored values as well as norms.

    To prevent a delimiter from being matched by Typst, and thus auto-scaled,
    escape it with a backslash. To instead disable auto-scaling completely, use
    `{set math.lr(size: 1em)}`.

    # Example
    ```example
    $ [a, b/2] $
    $ lr(]sum_(x=1)^n], size: #50%) x $
    $ abs((x + y) / 2) $
    $ \{ (x / y) \} $
    #set math.lr(size: 1em)
    $ { (a / b), a, b in (0; 1/2] } $
    ```

- name: calc
  title: Calculation
  category: foundations
  path: ["calc"]
  details: |
    Module for calculations and processing of numeric values.

    These definitions are part of the `calc` module and not imported by default.
    In addition to the functions listed below, the `calc` module also defines
    the constants `pi`, `tau`, `e`, and `inf`.

- name: std
  title: Standard library
  category: foundations
  path: ["std"]
  details: |
    A module that contains all globally accessible items.

    # Using "shadowed" definitions
    The `std` module is useful whenever you overrode a name from the global
    scope (this is called _shadowing_). For instance, you might have used the
    name `text` for a parameter. To still access the `text` element, write
    `std.text`.

    ```example
    >>> #set page(margin: (left: 3em))
    #let par = [My special paragraph.]
    #let special(text) = {
      set std.text(style: "italic")
      set std.par.line(numbering: "1")
      text
    }

    #special(par)

    #lorem(10)
    ```

    # Conditional access
    You can also use this in combination with the [dictionary
    constructor]($dictionary) to conditionally access global definitions. This
    can, for instance, be useful to use new or experimental functionality when
    it is available, while falling back to an alternative implementation if
    used on an older Typst version. In particular, this allows us to create
    [polyfills](https://en.wikipedia.org/wiki/Polyfill_(programming)).

    This can be as simple as creating an alias to prevent warning messages, for
    example, conditionally using `pattern` in Typst version 0.12, but using
    [`tiling`] in newer versions. Since the parameters accepted by the `tiling`
    function match those of the older `pattern` function, using the `tiling`
    function when available and falling back to `pattern` otherwise will unify
    the usage across all versions. Note that, when creating a polyfill,
    [`sys.version`]($category/foundations/sys) can also be very useful.

    ```typ
    #let tiling = if "tiling" in std { tiling } else { pattern }

    ...
    ```

- name: sys
  title: System
  category: foundations
  path: ["sys"]
  details: |
    Module for system interactions.

    This module defines the following items:

    - The `sys.version` constant (of type [`version`]) that specifies
      the currently active Typst compiler version.

    - The `sys.inputs` [dictionary], which makes external inputs
      available to the project. An input specified in the command line as
      `--input key=value` becomes available under `sys.inputs.key` as
      `{"value"}`. To include spaces in the value, it may be enclosed with
      single or double quotes.

      The value is always of type [string]($str). More complex data
      may be parsed manually using functions like [`json`]($json).

- name: sym
  title: General
  category: symbols
  path: ["sym"]
  details: |
    Named general symbols.

    For example, `#sym.arrow` produces the → symbol. Within
    [formulas]($category/math), these symbols can be used without the `#sym.`
    prefix.

    The `d` in an integral's `dx` can be written as `[$dif x$]`.
    Outside math formulas, `dif` can be accessed as `math.dif`.

- name: emoji
  title: Emoji
  category: symbols
  path: ["emoji"]
  details: |
    Named emojis.

    For example, `#emoji.face` produces the 😀 emoji. If you frequently use
    certain emojis, you can also import them from the `emoji` module (`[#import
    emoji: face]`) to use them without the `emoji.` prefix.
