---
description: |
  The Typst reference is a systematic and comprehensive guide to the Typst
  typesetting language.
---

# Reference
This reference documentation is a comprehensive guide to all of Typst's syntax,
concepts, types, and functions. If you are completely new to Typs<PERSON>, we recommend
starting with the [tutorial] and then coming back to the reference to learn more
about <PERSON><PERSON><PERSON>'s features as you need them.

## Language
The reference starts with a language part that gives an overview over
[Typst's syntax]($syntax) and contains information about concepts involved in
[styling documents,]($styling) using
[Typst's scripting capabilities.]($scripting)

## Functions
The second part includes chapters on all functions used to insert, style, transform,
and layout content in Typst documents. Each function is documented with a
description of its purpose, a list of its parameters, and examples of how to use
it.

The final part of the reference explains all functions that are used within
Typst's code mode to manipulate and transform data. Just as in the previous
part, each function is documented with a description of its purpose, a list of
its parameters, and examples of how to use it.
