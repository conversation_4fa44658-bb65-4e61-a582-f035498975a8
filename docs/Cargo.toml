[package]
name = "typst-docs"
version = { workspace = true }
rust-version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
publish = false

[[bin]]
name = "typst-docs"
required-features = ["cli"]
doc = false

[features]
default = ["cli"]
cli = ["clap", "typst-render", "serde_json"]

[dependencies]
typst = { workspace = true }
typst-render = { workspace = true, optional = true }
typst-utils = { workspace = true }
typst-assets = { workspace = true, features = ["fonts"] }
typst-dev-assets = { workspace = true }
clap = { workspace = true, optional = true }
codex = { workspace = true }
ecow = { workspace = true }
heck = { workspace = true }
pulldown-cmark = { workspace = true }
rustc-hash = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true, optional = true }
serde_yaml = { workspace = true }
syntect = { workspace = true, features = ["html"] }
typed-arena = { workspace = true }
unicode-math-class = { workspace = true }
unscanny = { workspace = true }
yaml-front-matter = { workspace = true }

[lints]
workspace = true
